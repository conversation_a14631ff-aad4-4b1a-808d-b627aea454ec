"""
UI游戏核心模块
专门处理游戏相关的UI事务和消息处理
"""
from PySide6.Qtsvg import QGraphicsSvgItem
from PySide6.QtCore import QObject
from threading import Timer


class UIGameCore(QObject):
    """UI游戏核心类
    
    负责处理所有游戏相关的UI逻辑：
    - 游戏事件消息处理
    - 游戏状态管理
    - 游戏UI控制
    """
    
    def __init__(self, main_window, logger):
        """初始化UI游戏核心
        
        Args:
            main_window: 主窗口引用
            logger: 日志记录器
        """
        super().__init__()
        self.main_window = main_window
        self.logger = logger
        self.pieces_dict = {}
        
        # 连接游戏相关信号
        self._connect_game_signals()
        
        self.logger.info("UI游戏核心模块初始化完成")
    
    def _connect_game_signals(self):
        """连接游戏相关信号"""
        # 连接游戏事件响应信号
        self.main_window.message_hub.signals.game_event_response.connect(self.handle_game_event)
        
        self.logger.debug("游戏信号连接完成")
    
    # ==================== 游戏消息处理方法 ====================
    
    def handle_game_event(self, message):
        """处理游戏事件的统一入口"""
        data = message.get("data", {})
        event_type = data.get("type")
        
        self.logger.info(f"处理游戏事件: {event_type}")
        
        if event_type == "start_game":
            self._handle_game_started(data)
        elif event_type == "pick_pieces":
            self.init_pieces(data)
        elif event_type == "end_game":
            self._handle_game_ended(data)
        elif event_type == "game_action":
            self._handle_game_action(data)
        else:
            self.logger.warning(f"未知的游戏事件类型: {event_type}")
    
    def _handle_game_started(self, data):
        """处理游戏开始消息"""
        room_name = data.get("game_room")
        members = data.get("members")
        self.logger.info(f"游戏开始: 房间={room_name}, 成员={members}")

        # 更新房间状态为游戏中
        self.main_window.move_room_to_playing(room_name, members)
        
        # 如果当前用户在游戏中，启动游戏界面
        if self.main_window.user_data['username'] in members:
            self.start_game()
        else:
            self.logger.info(f'{room_name}开始游戏了，无座位之人退出房间')
            self.main_window.tabs.removeTab(1)
    
    def _handle_game_ended(self, data):
        """处理游戏结束消息"""
        room_name = data.get("game_room")
        members = data.get("members")
        self.logger.info(f"游戏结束: 房间={room_name}, 成员={members}")
        
        # 更新房间状态为等待中
        self.main_window.move_room_to_waiting(room_name)
        
        # 如果当前用户在游戏中，结束游戏界面
        if self.main_window.user_data['username'] in members:
            self.end_game()
    
    def _handle_game_action(self, data):
        """处理游戏动作消息"""
        action_type = data.get("action_type")
        self.logger.info(f"处理游戏动作: {action_type}")
        
        # TODO: 根据具体的游戏动作类型进行处理
        # 例如：棋子移动、攻击、防御等
        pass
    
    # ==================== 游戏状态控制方法 ====================
    
    def start_game(self):
        """游戏开始，移除遮罩"""
        self.logger.info("启动游戏界面")
        
        # 隐藏游戏视图遮罩
        self.main_window.view_mask.hide()
        
        # 更新游戏状态
        self.main_window.I_am_playing = True
        
        # 隐藏座位选择框架
        self.main_window.seat_choose_frame.hide()
        
        # 显示游戏开始引导标志
        self.main_window.show_guide_signs(1)


    def end_game(self):
        """游戏结束，显示遮罩"""
        self.logger.info("结束游戏界面")
        
        # 显示游戏视图遮罩
        self.main_window.view_mask.show()
        
        # 更新游戏状态
        self.main_window.I_am_playing = False
        
        # 显示座位选择框架
        self.main_window.seat_choose_frame.show()
        
        # 显示游戏结束引导标志
        self.main_window.show_guide_signs(3)
        
        # 将房间移动到等待状态
        self.main_window.move_room_to_waiting(self.main_window.my_room)
    
    def pause_game(self):
        """暂停游戏"""
        self.logger.info("暂停游戏")
        # TODO: 实现游戏暂停逻辑
        pass
    
    def resume_game(self):
        """恢复游戏"""
        self.logger.info("恢复游戏")
        # TODO: 实现游戏恢复逻辑
        pass
    
    # ==================== 游戏数据处理方法 ====================

    def update_game_data(self, game_data):
        """更新游戏数据"""
        self.logger.debug(f"更新游戏数据: {len(game_data) if game_data else 0} 项")
        # TODO: 实现游戏数据更新逻辑
        pass

    def sync_game_state(self, game_state):
        """同步游戏状态"""
        self.logger.debug("同步游戏状态")
        # TODO: 实现游戏状态同步逻辑
        pass

    # ==================== 棋盘交互方法 ====================

    def init_pieces(self, data):
        """初始化棋子"""
        self.pieces_dict = data.get("piece_data")
        self.logger.debug("初始化棋子")
        for i in self.pieces_dict:
            if self.pieces_dict[i][0] is None:
                pic_name = '.\\image\\pieces\\blank.svg'
            elif self.pieces_dict[i][0] == self.main_window.my_seat:  # 只显示己方棋面，隐藏其他棋面
                pic_name = '.\\image\\pieces\\' + self.pieces_dict[i][1] + '\\' + self.pieces_dict[i][2] + '.svg'
            else:
                # pic_name = '.\\image\\pieces\\' + self.info_dic[i][1] + '\\' + 'back.svg'
                pic_name = '.\\image\\pieces\\' + self.pieces_dict[i][1] + '\\' + self.pieces_dict[i][2] + '.svg'
            '''根据参数判断是换队友重玩，还是不换队友重玩'''
            piece = Piece(pic_name, i, self.pieces_dict[i])  # 实例化棋子
            self.layout(self.obj_dic[i])


    def handle_scene_click(self, button, scene_pos):
        """处理棋盘点击事件"""
        self.logger.debug(f"棋盘点击: 按钮={button}, 位置=({scene_pos.x()}, {scene_pos.y()})")

        # 只有在游戏进行中才处理点击
        if not self.main_window.I_am_playing:
            return

        # TODO: 实现棋盘点击逻辑
        # 例如：选择棋子、移动棋子、攻击等
        pass

    def update_piece_position(self, piece_id, new_pos):
        """更新棋子位置"""
        self.logger.debug(f"更新棋子位置: {piece_id} -> {new_pos}")
        # TODO: 实现棋子位置更新逻辑
        pass

    def highlight_valid_moves(self, piece_id):
        """高亮显示有效移动位置"""
        self.logger.debug(f"高亮有效移动: {piece_id}")
        # TODO: 实现移动位置高亮逻辑
        pass
    
    # ==================== 清理方法 ====================
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("清理UI游戏核心资源")
        
        # 断开信号连接
        try:
            self.main_window.message_hub.signals.game_event_response.disconnect(self.handle_game_event)
        except Exception as e:
            self.logger.warning(f"断开游戏信号连接时出错: {e}")
        
        self.logger.info("UI游戏核心资源清理完成")


class Piece(QGraphicsSvgItem):  # 棋子类，继承于QGraphicsSvgItem类，可绑定矢量图片
    def __init__(self, pic_name, key, list1):
        super(Piece, self).__init__(pic_name)
        # self.setOpacity(0.5)
        if list1[0] is None:
            self.setOpacity(0.5)
        self.key = key
        self.code = list1[0]
        self.color = list1[1]
        self.rank = list1[2]
        self.pos = list1[3]
        self.flag = list1[4]  # 状态判断标志位，0：死亡　1：存活　2：人质
        # self.pick_switch = 0
        self.setTransformOriginPoint(31 / 2, 17 / 2)  # 变换基准点处于物体自身坐标系，影响一切变形效果，包括缩放和旋转
        self.setScale(game.chess.org_scale)  # 先变换为初始大小
        self.setScale(game.chess.org_scale * window.scale)  # 再变换为所需大小
        game.chess.obj_dic[key] = self
        window.scene.addItem(self)  # 将棋子对象置入场景，才能显示出来

    def __del__(self):  # 察看棋子对象是否被删除，释放内存
        print('del', self.key)

